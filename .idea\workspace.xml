<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a43ac40b-6382-4248-a873-5ac09dc35a6b" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2wibV6SrNhhy7MUE5C4fpXaMWqj" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "Python.airport_problem.executor": "Run",
    "Python.airport_problem_add.executor": "Run",
    "Python.base.executor": "Run",
    "Python.csv_compare.executor": "Run",
    "Python.data_loader.executor": "Run",
    "Python.find_776.executor": "Run",
    "Python.test.executor": "Run",
    "Python.test_angle.executor": "Run",
    "Python.test_improved_segments.executor": "Run",
    "Python.去重.executor": "Run",
    "Python.检查csv1是否全部存在于csv2.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/problem_all",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\mh\problem_all\problem_doh" />
      <recent name="C:\Users\<USER>\Desktop\mh\problem_all\problem_pek" />
      <recent name="C:\Users\<USER>\Desktop\mh\problem_all\problem_all" />
      <recent name="C:\Users\<USER>\Desktop\mh\problem_all" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-PY-251.26094.141" />
        <option value="bundled-python-sdk-9f8e2b94138c-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26094.141" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="a43ac40b-6382-4248-a873-5ac09dc35a6b" name="更改" comment="" />
      <created>1746527361987</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746527361987</updated>
      <workItem from="1746527363355" duration="367000" />
      <workItem from="1746527794254" duration="16282000" />
      <workItem from="1747026763690" duration="16958000" />
      <workItem from="1747361817995" duration="2728000" />
      <workItem from="1749695055423" duration="785000" />
      <workItem from="1749790929517" duration="8285000" />
      <workItem from="1749948152606" duration="3095000" />
      <workItem from="1750305090198" duration="5404000" />
      <workItem from="1750334940179" duration="12877000" />
      <workItem from="1750655055866" duration="3443000" />
      <workItem from="1750846741012" duration="5162000" />
      <workItem from="1750894544962" duration="1189000" />
      <workItem from="1750898378880" duration="1693000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/problem_all$csv1csv2.coverage" NAME="检查csv1是否全部存在于csv2 覆盖结果" MODIFIED="1750348677183" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/problem_all$data_loader.coverage" NAME="data_loader 覆盖结果" MODIFIED="1750898407378" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/problem_doh" />
    <SUITE FILE_PATH="coverage/problem_all$test_angle.coverage" NAME="test_angle 覆盖结果" MODIFIED="1750334946713" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/problem_all$test.coverage" NAME="test 覆盖结果" MODIFIED="1750333814720" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/problem_all$airport_problem_add.coverage" NAME="airport_problem_add 覆盖结果" MODIFIED="1750865022533" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/problem_man" />
    <SUITE FILE_PATH="coverage/problem_all$airport_problem.coverage" NAME="airport_problem 覆盖结果" MODIFIED="1750899450646" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/problem_doh" />
    <SUITE FILE_PATH="coverage/problem_all$csv_compare.coverage" NAME="csv_compare 覆盖结果" MODIFIED="1750346218144" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/problem_all$test_improved_segments.coverage" NAME="test_improved_segments 覆盖结果" MODIFIED="1750898426316" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/problem_all$base.coverage" NAME="base 覆盖结果" MODIFIED="1750338293431" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/problem_all$.coverage" NAME="去重 覆盖结果" MODIFIED="1750864261697" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/problem_doh" />
    <SUITE FILE_PATH="coverage/problem_all$find_776.coverage" NAME="find_776 覆盖结果" MODIFIED="1750343456293" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>